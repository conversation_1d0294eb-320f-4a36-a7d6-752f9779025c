---
// Import components and utils similar to SinglePost.astro
import { Icon } from 'astro-icon/components';
import Image from '~/components/common/Image.astro';
import SocialShare from '~/components/common/SocialShare.astro';
import TableOfContents from '~/components/blog/TableOfContents.astro';
import FYINotice from '~/components/blog/FYINotice.astro';
import UpdateNotice from '~/components/blog/UpdateNotice.astro';
import HostingRecommendations from '~/components/blog/HostingRecommendations.astro';
import { getPermalink } from '~/utils/permalinks';
import { getFormattedDate } from '~/utils/utils';
import type { Post } from '~/types';

export interface Props {
  post: Post;
  url: string | URL;
  headings?: Array<{ depth: number; slug: string; text: string }>;
  noticeType?: 'fyi' | 'update' | null;
  noticeDate?: Date | string;
}

const { post, url, headings = [], noticeType = null, noticeDate } = Astro.props;
const authorDescription = "Hosting Reviewer di PenasihatHosting.com."; // Example description - customize as needed
---

<section class="mx-auto">
  {/* Simplified Disclosure Bar - Wirecutter style */}
  <div class="max-w-global mx-auto px-4 sm:px-6 my-6 text-center">
    <p class="text-xs text-gray-600 dark:text-gray-400">
      Kami mereview hosting secara independen. Ketika Anda membeli hosting melalui link kami, kami mendapatkan komisi (10-50%), tanpa ada biaya tambahan untuk Anda.
    </p>
  </div>

  <article>
    <header class="mb-2 relative">
      {/* Title Section - With Wirecutter style thick top border */}
      <div class="max-w-global mx-auto px-4 sm:px-6 mb-8 relative">
        <div class="max-w-full lg:max-w-[65ch]">
          <div class="text-[14px] font-medium text-neutral-500 hover:underline uppercase flex items-center gap-2 mb-4">
            {post.category && (
              <a 
                href={getPermalink(post.category.slug, 'category')}
              >
                {post.category.title}
              </a>
            )}
            
            {/* Right arrow separator */}
            {post.category && post.tags && post.tags.length > 0 && (
              <Icon name="tabler:chevron-right" class="w-4 h-4 text-neutral-400" />
            )}
            
            {/* Show the first tag next to category */}
            {post.tags && post.tags.length > 0 && (
              <a 
                href={getPermalink(post.tags[0].slug, 'tag')}
              >
                {post.tags[0].title}
              </a>
            )}
          </div>
          <div class="border-t-8 border-primary dark:border-primary pt-2 relative mx-auto">
            <h1 class="text-4xl md:text-5xl font-extrabold leading-tight tracking-tight font-heading mb-3 text-gray-900 dark:text-white">
              {post.title}
            </h1>
            
            <p class="text-base text-gray-600 dark:text-gray-300 mb-4">
              {post.excerpt}
            </p>
            
            {/* Updated Date - Below title */}
            <div class="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-4">
              <span>Updated <span class="font-medium">{getFormattedDate(post.updateDate || post.publishDate)}</span></span>
            </div>
            
            {/* Social Share - Below updated date */}
            <SocialShare url={url} text={post.title} class="flex gap-2" />
            
            {/* Update Notice - Moved to header/hero section */}
            {noticeType === 'update' && (
              <div class="mt-6">
                <UpdateNotice date={noticeDate} />
              </div>
            )}
          </div>
        </div>
      </div>
    </header>

    {/* Featured Image - Added back below header */}
    {post.image ? (
      <div class="max-w-global mx-auto mt-8 mb-2 sm:px-6">
        <Image
          src={post.image}
          class="w-full sm:rounded-md shadow-lg"
          widths={[400, 900, 1075]}
          sizes="100vw"
          alt={post?.excerpt || post.title || ''}
          width={1075}
          height={605}
          loading="lazy"
          decoding="async"
        />
        {/* Image shadow added for consistency */}
        {/* Assuming 16:9 aspect ratio */}
      </div>
    ) : null}

    {/* Content Section - Two Columns */}
    <div class="lg:grid lg:grid-cols-3 lg:gap-20 max-w-global mx-auto px-4 sm:px-6">
      {/* Main Content - 2/3 Width */}
      <div class="lg:col-span-2">
        {/* Author Card - Moved inside 2/3 content */}
        <div>
          <div class="py-4">
            {post.author && (
              <div class="flex items-center">
                <div class="w-12 h-12 rounded-full bg-gradient-to-br from-primary to-secondary dark:from-primary dark:to-secondary flex items-center justify-center text-white font-bold text-lg mr-4">
                  {post.author.charAt(0).toUpperCase()}
                </div>
                <div>
                  <span class="block text-base font-medium text-gray-900 dark:text-white">{post.author}</span>
                  <span class="text-sm text-gray-500 dark:text-gray-400">{authorDescription}</span>
                </div>
              </div>
            )}
          </div>
        </div>
        
        <div class="prose prose-base dark:prose-invert dark:prose-headings:text-neutral-200 prose-headings:font-heading prose-headings:leading-tight prose-headings:tracking-tight prose-headings:font-bold prose-a:font-bold prose-a:text-[var(--aw-color-accent)] prose-a:hover:text-[var(--aw-color-accent-hover)] dark:prose-a:text-[var(--aw-color-accent)] dark:prose-a:hover:text-[var(--aw-color-accent-hover)] prose-img:rounded-md prose-headings:scroll-mt-[80px] max-w-none leading-normal">
          {/* Wirecutter style section headers */}
          <slot />
        </div>
        
        {/* Footer - ToBlogLink removed */}
        <div class="my-6 border-t border-gray-200 dark:border-gray-800 flex flex-col items-start gap-6">
          
        </div>
      </div>
      
      {/* Sidebar - Complete restructuring */}
      <div class="lg:col-span-1">
        {/* Static content first */}
        <div class="mt-20 lg:mt-24 mb-10">
            {noticeType === 'fyi' && <FYINotice date={noticeDate} />}
          
          <div class="bg-neutral-100 dark:bg-slate-800 p-6 rounded-md mb-6">
              <TableOfContents headings={headings} />
            </div>
          </div>

          {/* Sticky hosting recommendations */}
        <div 
          style="position: -webkit-sticky; position: sticky; top: 6rem; z-index: 10;"
        >
            <HostingRecommendations />
          </div>
        </div>
    </div>
  </article>
</section> 

<style>
  /* Target li elements specifically within the prose content area of this layout */
  div.prose :global(li) {
    margin-top: 0;
    margin-bottom: 0;
  }
</style> 