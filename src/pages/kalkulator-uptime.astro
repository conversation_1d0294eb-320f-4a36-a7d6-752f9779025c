---
import Layout from '~/layouts/PageLayout.astro';
import { Icon } from 'astro-icon/components';

const metadata = {
  title: 'Kalkulator Uptime & Downtime - Hitung Waktu Online/Offline Website | Penasihat Hosting',
  description: 'Kalkulator uptime dan downtime untuk menghitung waktu online/offline website berdasarkan persentase. Ketahui berapa lama website akan offline atau online dalam setahun, sebulan, seminggu, atau sehari.',
  openGraph: {
    images: [
      {
        url: 'https://img.penasihathosting.com/2025/May/kalkulator-uptime.webp',
      }
    ]
  }
};
---

<Layout metadata={metadata}>
  <!-- Hero Section - Matching HomeHero design -->
  <section class="bg-white dark:bg-dark py-8">
    <div class="max-w-global mx-auto px-4 sm:px-6">
      <div class="text-xs text-gray-600 dark:text-gray-400 mb-6">
        Tool gratis untuk menghitung estimasi uptime dan downtime berdasarkan persentase hosting Anda.
      </div>
      <div class="max-w-full lg:max-w-[65ch]">
        <div class="border-t-8 border-primary dark:border-primary pt-2 mb-4">
          <h1 class="text-4xl md:text-5xl font-extrabold text-gray-900 dark:text-white mb-3">
            Kalkulator Uptime & Downtime
          </h1>
          <p class="text-lg text-gray-600 dark:text-gray-300 mb-4">
            Hitung waktu uptime dan downtime berdasarkan persentase hosting Anda. Ketahui berapa lama website akan online atau offline dalam periode waktu tertentu dengan dua kalkulator yang mudah digunakan.
          </p>
          <div class="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-4">
            <span class="font-medium">Penasihat Hosting</span>
            <span class="mx-2">•</span>
            <span>Tool Gratis</span>
            <span class="mx-2">•</span>
            <span>Dual Calculator</span>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Main Content Section - Matching IntroductionSection layout -->
  <section class="bg-white dark:bg-dark pb-12">
    <div class="max-w-global mx-auto px-4 sm:px-6">
      <div class="">
        <!-- Main Content - 2/3 Width -->
        <div class="">
          <!-- Tab Interface -->
          <div class="py-4 sm:py-6 bg-gray-50 dark:bg-gray-800 px-4 sm:px-6 rounded-lg mb-6">
            <!-- Tab Navigation -->
            <div class="flex space-x-1 bg-gray-200 dark:bg-gray-700 p-1 rounded-lg mb-6">
              <button
                id="uptimeTab"
                class="tab-btn active flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors duration-200 flex items-center justify-center"
              >
                <Icon name="tabler:trending-up" class="w-4 h-4 mr-2" />
                Kalkulator Uptime
              </button>
              <button
                id="downtimeTab"
                class="tab-btn flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors duration-200 flex items-center justify-center"
              >
                <Icon name="tabler:trending-down" class="w-4 h-4 mr-2" />
                Kalkulator Downtime
              </button>
            </div>

            <!-- Uptime Calculator Content -->
            <div id="uptimeContent" class="tab-content">
              <h2 class="text-xl font-bold text-gray-700 dark:text-gray-200 mb-6">Kalkulator Uptime</h2>

              <form id="uptimeForm" class="space-y-6">
                <div>
                  <label for="uptimeInput" class="block text-sm font-medium text-[var(--aw-color-text-default)] mb-2">
                    Persentase Uptime (%)
                  </label>
                  <div class="relative">
                    <input
                      type="number"
                      id="uptimeInput"
                      name="uptime"
                      min="0"
                      max="100"
                      step="0.001"
                      placeholder="99.9"
                      class="w-full px-4 py-3 text-lg border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-[var(--aw-color-text-default)] focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                    />
                    <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                      <span class="text-[var(--aw-color-text-muted)] text-lg">%</span>
                    </div>
                  </div>
                  <div id="uptimeErrorMessage" class="mt-2 text-sm text-red-600 dark:text-red-400 hidden"></div>
                </div>

                <!-- Quick Preset Buttons -->
                <div class="space-y-3">
                  <p class="text-sm font-medium text-[var(--aw-color-text-muted)]">Atau pilih preset umum:</p>
                  <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                    <button type="button" class="uptime-preset-btn" data-value="99">99%</button>
                    <button type="button" class="uptime-preset-btn" data-value="99.9">99.9%</button>
                    <button type="button" class="uptime-preset-btn" data-value="99.99">99.99%</button>
                    <button type="button" class="uptime-preset-btn" data-value="99.999">99.999%</button>
                  </div>
                </div>

                <button
                  type="submit"
                  class="btn-primary w-full py-3 px-6 rounded-lg font-semibold flex items-center justify-center"
                >
                  <Icon name="tabler:calculator" class="w-5 h-5 mr-2" />
                  Hitung Downtime
                </button>
              </form>
            </div>

            <!-- Downtime Calculator Content -->
            <div id="downtimeContent" class="tab-content hidden">
              <h2 class="text-xl font-bold text-gray-700 dark:text-gray-200 mb-6">Kalkulator Downtime</h2>

              <form id="downtimeForm" class="space-y-6">
                <div>
                  <label for="downtimeInput" class="block text-sm font-medium text-[var(--aw-color-text-default)] mb-2">
                    Persentase Downtime (%)
                  </label>
                  <div class="relative">
                    <input
                      type="number"
                      id="downtimeInput"
                      name="downtime"
                      min="0"
                      max="100"
                      step="0.001"
                      placeholder="0.1"
                      class="w-full px-4 py-3 text-lg border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-[var(--aw-color-text-default)] focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                    />
                    <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                      <span class="text-[var(--aw-color-text-muted)] text-lg">%</span>
                    </div>
                  </div>
                  <div id="downtimeErrorMessage" class="mt-2 text-sm text-red-600 dark:text-red-400 hidden"></div>
                </div>

                <!-- Quick Preset Buttons -->
                <div class="space-y-3">
                  <p class="text-sm font-medium text-[var(--aw-color-text-muted)]">Atau pilih preset umum:</p>
                  <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                    <button type="button" class="downtime-preset-btn" data-value="1">1%</button>
                    <button type="button" class="downtime-preset-btn" data-value="0.1">0.1%</button>
                    <button type="button" class="downtime-preset-btn" data-value="0.01">0.01%</button>
                    <button type="button" class="downtime-preset-btn" data-value="0.001">0.001%</button>
                  </div>
                </div>

                <button
                  type="submit"
                  class="btn-primary w-full py-3 px-6 rounded-lg font-semibold flex items-center justify-center"
                >
                  <Icon name="tabler:calculator" class="w-5 h-5 mr-2" />
                  Hitung Uptime
                </button>
              </form>
            </div>
          </div>

          <!-- Results Display -->
          <div id="resultsContainer" class="hidden">
            <div class="py-4 sm:py-6 bg-gray-50 dark:bg-gray-800 px-4 sm:px-6 rounded-lg">
              <!-- Uptime Results -->
              <div id="uptimeResults">
                <h3 class="text-xl font-bold text-gray-700 dark:text-gray-200 mb-6 flex items-center">
                  <Icon name="tabler:clock-x" class="w-6 h-6 mr-3 text-red-500" />
                  Estimasi Waktu Downtime
                </h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <!-- Per Year -->
                  <div class="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                      <Icon name="tabler:calendar-time" class="w-5 h-5 text-red-600 dark:text-red-400 mr-2" />
                      <h4 class="font-semibold text-gray-800 dark:text-gray-100">Per Tahun</h4>
                    </div>
                    <p id="yearlyDowntime" class="text-lg font-bold text-red-600 dark:text-red-400">-</p>
                  </div>

                  <!-- Per Month -->
                  <div class="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                      <Icon name="tabler:calendar-event" class="w-5 h-5 text-orange-600 dark:text-orange-400 mr-2" />
                      <h4 class="font-semibold text-gray-800 dark:text-gray-100">Per Bulan</h4>
                    </div>
                    <p id="monthlyDowntime" class="text-lg font-bold text-orange-600 dark:text-orange-400">-</p>
                  </div>

                  <!-- Per Week -->
                  <div class="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                      <Icon name="tabler:calendar-stats" class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" />
                      <h4 class="font-semibold text-gray-800 dark:text-gray-100">Per Minggu</h4>
                    </div>
                    <p id="weeklyDowntime" class="text-lg font-bold text-yellow-600 dark:text-yellow-400">-</p>
                  </div>

                  <!-- Per Day -->
                  <div class="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                      <Icon name="tabler:calendar" class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
                      <h4 class="font-semibold text-gray-800 dark:text-gray-100">Per Hari</h4>
                    </div>
                    <p id="dailyDowntime" class="text-lg font-bold text-blue-600 dark:text-blue-400">-</p>
                  </div>
                </div>
              </div>

              <!-- Downtime Results -->
              <div id="downtimeResults" class="hidden">
                <h3 class="text-xl font-bold text-gray-700 dark:text-gray-200 mb-6 flex items-center">
                  <Icon name="tabler:clock-check" class="w-6 h-6 mr-3 text-green-500" />
                  Estimasi Waktu Uptime
                </h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <!-- Per Year -->
                  <div class="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                      <Icon name="tabler:calendar-time" class="w-5 h-5 text-green-600 dark:text-green-400 mr-2" />
                      <h4 class="font-semibold text-gray-800 dark:text-gray-100">Per Tahun</h4>
                    </div>
                    <p id="yearlyUptime" class="text-lg font-bold text-green-600 dark:text-green-400">-</p>
                  </div>

                  <!-- Per Month -->
                  <div class="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                      <Icon name="tabler:calendar-event" class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
                      <h4 class="font-semibold text-gray-800 dark:text-gray-100">Per Bulan</h4>
                    </div>
                    <p id="monthlyUptime" class="text-lg font-bold text-blue-600 dark:text-blue-400">-</p>
                  </div>

                  <!-- Per Week -->
                  <div class="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                      <Icon name="tabler:calendar-stats" class="w-5 h-5 text-purple-600 dark:text-purple-400 mr-2" />
                      <h4 class="font-semibold text-gray-800 dark:text-gray-100">Per Minggu</h4>
                    </div>
                    <p id="weeklyUptime" class="text-lg font-bold text-purple-600 dark:text-purple-400">-</p>
                  </div>

                  <!-- Per Day -->
                  <div class="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                      <Icon name="tabler:calendar" class="w-5 h-5 text-indigo-600 dark:text-indigo-400 mr-2" />
                      <h4 class="font-semibold text-gray-800 dark:text-gray-100">Per Hari</h4>
                    </div>
                    <p id="dailyUptime" class="text-lg font-bold text-indigo-600 dark:text-indigo-400">-</p>
                  </div>
                </div>

                <!-- Additional Uptime Percentage Display -->
                <div class="mt-6 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                  <div class="flex items-center mb-2">
                    <Icon name="tabler:percentage" class="w-5 h-5 text-primary mr-2" />
                    <h4 class="font-semibold text-gray-800 dark:text-gray-100">Persentase Uptime yang Dihasilkan</h4>
                  </div>
                  <p id="calculatedUptime" class="text-2xl font-bold text-primary">-</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Placeholder Content (shown before calculation) -->
          <div id="placeholderContent">
            <div class="py-4 sm:py-6 bg-gray-50 dark:bg-gray-800 px-4 sm:px-6 rounded-lg">
              <h3 class="text-xl font-bold text-gray-700 dark:text-gray-200 mb-6 flex items-center">
                <Icon name="tabler:info-circle" class="w-6 h-6 mr-3 text-primary" />
                Contoh Perhitungan Uptime
              </h3>

              <div class="prose prose-base dark:prose-invert max-w-none">
                <p>Berikut adalah contoh perhitungan downtime untuk berbagai tingkat uptime yang umum digunakan:</p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                  <!-- Example: 99% -->
                  <div class="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <h4 class="font-semibold text-gray-800 dark:text-gray-100 mb-2">99% Uptime</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-300">
                      <strong>Per tahun:</strong> 3.65 hari<br>
                      <strong>Per bulan:</strong> 7.31 jam
                    </p>
                  </div>

                  <!-- Example: 99.9% -->
                  <div class="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <h4 class="font-semibold text-gray-800 dark:text-gray-100 mb-2">99.9% Uptime</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-300">
                      <strong>Per tahun:</strong> 8.77 jam<br>
                      <strong>Per bulan:</strong> 43.8 menit
                    </p>
                  </div>

                  <!-- Example: 99.99% -->
                  <div class="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <h4 class="font-semibold text-gray-800 dark:text-gray-100 mb-2">99.99% Uptime</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-300">
                      <strong>Per tahun:</strong> 52.6 menit<br>
                      <strong>Per bulan:</strong> 4.38 menit
                    </p>
                  </div>

                  <!-- Example: 99.999% -->
                  <div class="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <h4 class="font-semibold text-gray-800 dark:text-gray-100 mb-2">99.999% Uptime</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-300">
                      <strong>Per tahun:</strong> 5.26 menit<br>
                      <strong>Per bulan:</strong> 26.3 detik
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- New Comprehensive Content Sections -->

          <div class="py-6 space-y-8">
            <div class="bg-gray-50 dark:bg-gray-800 px-4 sm:px-6 py-6 rounded-lg">
              <h3 class="text-xl font-bold text-gray-700 dark:text-gray-200 mb-4">Apa Itu Uptime dan Mengapa Penting?</h3>
              <div class="prose prose-base dark:prose-invert max-w-none">
                <p>
                  Uptime adalah metrik yang mengukur total waktu layanan (seperti website atau server hosting) tersedia dan beroperasi dalam periode waktu tertentu. Biasanya dinyatakan dalam persentase. Angka uptime yang tinggi menunjukkan bahwa layanan tersebut jarang mengalami gangguan atau downtime.
                </p>
                <p>
                  Mengapa uptime penting? Bagi sebagian besar website, terutama yang digunakan untuk bisnis, e-commerce, atau layanan online, ketersediaan yang tinggi adalah krusial. Setiap menit downtime dapat berarti kerugian pendapatan, pengalaman pengguna yang buruk, rusaknya reputasi merek, dan bahkan dampak negatif pada peringkat mesin pencari karena Google dan mesin pencari lainnya mempertimbangkan ketersediaan situs.
                </p>
                <p>
                  Kalkulator ini membantu Anda memvisualisasikan dampak dari persentase uptime tertentu menjadi total waktu downtime yang bisa terjadi dalam setahun, sebulan, seminggu, atau sehari.
                </p>
              </div>
            </div>

            <div class="bg-gray-50 dark:bg-gray-800 px-4 sm:px-6 py-6 rounded-lg">
              <h3 class="text-xl font-bold text-gray-700 dark:text-gray-200 mb-4">Memahami Angka Uptime dalam Realitas</h3>
              <div class="prose prose-base dark:prose-invert max-w-none">
                <p>
                  Melihat angka persentase uptime seperti 99.9% mungkin terlihat sempurna, namun penting untuk mengetahui berapa banyak waktu downtime yang sebenarnya direpresentasikan oleh angka tersebut dalam periode waktu yang lebih mudah dipahami. Berikut adalah perkiraan downtime untuk persentase uptime umum berdasarkan perhitungan standar (365.25 hari/tahun, 30.44 hari/bulan):
                </p>
                <ul class="list-disc list-inside">
                  <li><strong>99.9% Uptime:</strong> Sekitar 8 jam 46 menit downtime per tahun, atau 43.8 menit per bulan.</li>
                  <li><strong>99.99% Uptime:</strong> Sekitar 52 menit 36 detik downtime per tahun, atau 4.38 menit per bulan.</li>
                  <li><strong>99.999% Uptime:</strong> Sekitar 5 menit 15 detik downtime per tahun, atau 26.3 detik per bulan.</li>
                </ul>
                <p class="mt-4">
                  Angka-angka ini menunjukkan bahwa perbedaan desimal kecil pada persentase uptime dapat berarti perbedaan jam atau hari downtime dalam setahun. Memilih hosting dengan uptime guarantee yang lebih tinggi sangat penting untuk website atau aplikasi yang membutuhkan ketersediaan maksimal.
                </p>
              </div>
            </div>

             <div class="bg-gray-50 dark:bg-gray-800 px-4 sm:px-6 py-6 rounded-lg">
              <h3 class="text-xl font-bold text-gray-700 dark:text-gray-200 mb-4">Faktor yang Mempengaruhi Uptime Hosting</h3>
              <div class="prose prose-base dark:prose-invert max-w-none">
                <p>
                  Uptime hosting dipengaruhi oleh berbagai faktor. Beberapa yang paling penting meliputi:
                </p>
                <ul class="list-disc list-inside">
                  <li><strong>Kualitas Infrastruktur Server:</strong> Hardware yang handal, jaringan yang stabil, dan pusat data yang aman.</li>
                  <li><strong>Pemeliharaan dan Monitoring:</strong> Proaktif dalam pemeliharaan rutin dan sistem monitoring yang mendeteksi masalah dengan cepat.</li>
                  <li><strong>Keamanan:</strong> Perlindungan terhadap serangan DDoS atau upaya hacking yang dapat menyebabkan server down.</li>
                  <li><strong>Load Balancing dan Redundansi:</strong> Kemampuan untuk mendistribusikan traffic dan memiliki sistem cadangan jika terjadi kegagalan komponen.</li>
                  <li><strong>Dukungan Teknis:</strong> Tim support yang responsif dan kompeten untuk menyelesaikan masalah dengan cepat.</li>
                </ul>
                 <p class="mt-4">
                  Memilih penyedia hosting yang berinvestasi dalam area-area ini cenderung menawarkan uptime yang lebih baik.
                </p>
              </div>
            </div>

             <div class="bg-gray-50 dark:bg-gray-800 px-4 sm:px-6 py-6 rounded-lg">
              <h3 class="text-xl font-bold text-gray-700 dark:text-gray-200 mb-4">Memilih Hosting Berdasarkan Uptime Guarantee</h3>
              <div class="prose prose-base dark:prose-invert max-w-none">
                <p>
                  Sebagian besar penyedia hosting menawarkan 'Uptime Guarantee' atau 'Service Level Agreement' (SLA) yang menjanjikan persentase uptime minimum. Penting untuk membaca dan memahami SLA ini, termasuk:
                </p>
                <ul class="list-disc list-inside">
                  <li><strong>Persentase Guarantee:</strong> Angka minimum uptime yang mereka janjikan (misalnya, 99.9%).</li>
                  <li><strong>Proses Klaim:</strong> Bagaimana Anda bisa mengklaim kompensasi jika uptime jatuh di bawah guarantee.</li>
                  <li><strong>Pengecualian:</strong> Situasi apa saja yang tidak termasuk dalam guarantee (misalnya, pemeliharaan terjadwal).</li>
                </ul>
                 <p class="mt-4">
                  Jangan hanya terpaku pada angka tertinggi. Pertimbangkan kebutuhan spesifik website Anda dan seberapa kritis ketersediaan bagi operasi Anda. Untuk bisnis online, SLA 99.9% atau lebih tinggi seringkali dianggap sebagai standar minimum yang baik.
                </p>
              </div>
            </div>

            <div class="bg-gray-50 dark:bg-gray-800 px-4 sm:px-6 py-6 rounded-lg">
              <h3 class="text-xl font-bold text-gray-700 dark:text-gray-200 mb-4">Cara Memonitor Uptime Website Anda</h3>
              <div class="prose prose-base dark:prose-invert max-w-none">
                <p>
                  Meskipun hosting Anda memberikan guarantee, memonitor uptime website Anda secara independen adalah praktik yang baik. Ini memungkinkan Anda memverifikasi janji provider dan mendeteksi masalah yang mungkin tidak langsung mereka sadari. Beberapa cara untuk memonitor uptime:
                </p>
                <ul class="list-disc list-inside">
                  <li><strong>Layanan Monitoring Uptime Online:</strong> Banyak layanan gratis dan berbayar yang secara otomatis memeriksa website Anda dari berbagai lokasi di seluruh dunia secara berkala.</li>
                  <li><strong>Plugin Website:</strong> Beberapa platform (seperti WordPress) memiliki plugin monitoring uptime.</li>
                  <li><strong>Google Search Console:</strong> Dapat memberikan notifikasi jika Googlebot mengalami kesulitan mengakses situs Anda secara teratur.</li>
                </ul>
                <p class="mt-4">
                  Memonitor uptime memberi Anda data objektif tentang kinerja hosting Anda dan membantu Anda mengambil tindakan jika terjadi masalah.
                </p>
              </div>
            </div>

          </div>

        </div>


      </div>
    </div>
  </section>

</Layout>

<style>
  /* Matching existing site design patterns */
  .uptime-preset-btn, .downtime-preset-btn {
    @apply px-3 py-2 text-sm font-medium border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 rounded-lg hover:bg-primary hover:text-white hover:border-primary transition-colors duration-200;
  }

  .uptime-preset-btn.active, .downtime-preset-btn.active {
    @apply bg-primary text-white border-primary;
  }

  /* Tab styling following NYC design patterns */
  .tab-btn {
    @apply text-gray-600 dark:text-gray-400 bg-transparent hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-600;
  }

  .tab-btn.active {
    @apply bg-white dark:bg-gray-800 text-gray-900 dark:text-white shadow-sm;
  }

  /* Tab content transitions */
  .tab-content {
    @apply transition-all duration-300 ease-in-out;
  }

  .tab-content.hidden {
    @apply opacity-0 pointer-events-none;
  }

  /* Simple animations matching site style */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }

  /* Focus states matching site patterns */
  input:focus {
    outline: none;
  }

  /* Responsive tab adjustments */
  @media (max-width: 640px) {
    .tab-btn {
      @apply text-xs px-2 py-2;
    }
  }
</style>

<script>
  // Dual Calculator Logic - Enhanced with Tab Interface and Downtime Calculator
  class DualCalculator {
    constructor() {
      // Tab elements
      this.uptimeTab = document.getElementById('uptimeTab');
      this.downtimeTab = document.getElementById('downtimeTab');
      this.uptimeContent = document.getElementById('uptimeContent');
      this.downtimeContent = document.getElementById('downtimeContent');

      // Uptime calculator elements
      this.uptimeForm = document.getElementById('uptimeForm');
      this.uptimeInput = document.getElementById('uptimeInput');
      this.uptimeErrorMessage = document.getElementById('uptimeErrorMessage');
      this.uptimePresetButtons = document.querySelectorAll('.uptime-preset-btn');

      // Downtime calculator elements
      this.downtimeForm = document.getElementById('downtimeForm');
      this.downtimeInput = document.getElementById('downtimeInput');
      this.downtimeErrorMessage = document.getElementById('downtimeErrorMessage');
      this.downtimePresetButtons = document.querySelectorAll('.downtime-preset-btn');

      // Results elements
      this.resultsContainer = document.getElementById('resultsContainer');
      this.placeholderContent = document.getElementById('placeholderContent');
      this.uptimeResults = document.getElementById('uptimeResults');
      this.downtimeResults = document.getElementById('downtimeResults');

      // Current active calculator
      this.activeCalculator = 'uptime';

      // Check if all required elements exist
      if (!this.uptimeTab || !this.downtimeTab || !this.uptimeContent || !this.downtimeContent) {
        console.error('Required tab elements not found');
        return;
      }

      this.initEventListeners();
    }

    initEventListeners() {
      // Tab switching
      this.uptimeTab.addEventListener('click', () => this.switchTab('uptime'));
      this.downtimeTab.addEventListener('click', () => this.switchTab('downtime'));

      // Uptime calculator events
      if (this.uptimeForm && this.uptimeInput) {
        this.uptimeForm.addEventListener('submit', (e) => {
          e.preventDefault();
          this.calculateFromUptime();
        });

        this.uptimeInput.addEventListener('input', () => {
          this.clearError('uptime');
          if (this.uptimeInput.value.trim()) {
            this.calculateFromUptime();
          } else {
            this.hideResults();
          }
        });
      }

      // Downtime calculator events
      if (this.downtimeForm && this.downtimeInput) {
        this.downtimeForm.addEventListener('submit', (e) => {
          e.preventDefault();
          this.calculateFromDowntime();
        });

        this.downtimeInput.addEventListener('input', () => {
          this.clearError('downtime');
          if (this.downtimeInput.value.trim()) {
            this.calculateFromDowntime();
          } else {
            this.hideResults();
          }
        });
      }

      // Preset buttons for uptime
      this.uptimePresetButtons.forEach((btn) => {
        btn.addEventListener('click', () => {
          const value = btn.dataset.value;
          if (this.uptimeInput && value) {
            this.uptimeInput.value = value;
            this.updateActivePreset(btn, 'uptime');
            this.calculateFromUptime();
          }
        });
      });

      // Preset buttons for downtime
      this.downtimePresetButtons.forEach((btn) => {
        btn.addEventListener('click', () => {
          const value = btn.dataset.value;
          if (this.downtimeInput && value) {
            this.downtimeInput.value = value;
            this.updateActivePreset(btn, 'downtime');
            this.calculateFromDowntime();
          }
        });
      });
    }

    switchTab(calculator) {
      this.activeCalculator = calculator;

      // Update tab buttons
      this.uptimeTab.classList.toggle('active', calculator === 'uptime');
      this.downtimeTab.classList.toggle('active', calculator === 'downtime');

      // Update content visibility
      this.uptimeContent.classList.toggle('hidden', calculator !== 'uptime');
      this.downtimeContent.classList.toggle('hidden', calculator !== 'downtime');

      // Update results visibility
      if (this.resultsContainer && !this.resultsContainer.classList.contains('hidden')) {
        this.uptimeResults.classList.toggle('hidden', calculator !== 'uptime');
        this.downtimeResults.classList.toggle('hidden', calculator !== 'downtime');
      }

      // Clear inputs and results when switching
      this.clearInputs();
      this.hideResults();
    }

    clearInputs() {
      if (this.uptimeInput) this.uptimeInput.value = '';
      if (this.downtimeInput) this.downtimeInput.value = '';
      this.clearError('uptime');
      this.clearError('downtime');
      this.clearActivePresets();
    }

    clearActivePresets() {
      this.uptimePresetButtons.forEach((btn) => btn.classList.remove('active'));
      this.downtimePresetButtons.forEach((btn) => btn.classList.remove('active'));
    }

    updateActivePreset(activeBtn, type) {
      const buttons = type === 'uptime' ? this.uptimePresetButtons : this.downtimePresetButtons;
      buttons.forEach((btn) => btn.classList.remove('active'));
      activeBtn.classList.add('active');
    }

    validateInput(value, type) {
      if (isNaN(value) || value < 0 || value > 100) {
        this.showError(`Masukkan nilai antara 0 dan 100`, type);
        return false;
      }
      return true;
    }

    showError(message, type) {
      const errorElement = type === 'uptime' ? this.uptimeErrorMessage : this.downtimeErrorMessage;
      if (errorElement) {
        errorElement.textContent = message;
        errorElement.classList.remove('hidden');
      }
    }

    clearError(type) {
      const errorElement = type === 'uptime' ? this.uptimeErrorMessage : this.downtimeErrorMessage;
      if (errorElement) {
        errorElement.classList.add('hidden');
      }
    }

    calculateFromUptime() {
      const uptimeValue = parseFloat(this.uptimeInput.value || '0');

      if (!this.validateInput(uptimeValue, 'uptime')) {
        this.hideResults();
        return;
      }

      this.clearError('uptime');

      // Calculate downtime percentage
      const downtimePercentage = 100 - uptimeValue;

      // Time periods in seconds
      const secondsPerYear = 365.25 * 24 * 60 * 60; // Account for leap years
      const secondsPerMonth = 30.44 * 24 * 60 * 60; // Average month
      const secondsPerWeek = 7 * 24 * 60 * 60;
      const secondsPerDay = 24 * 60 * 60;

      // Calculate downtime in seconds for each period
      const yearlyDowntimeSeconds = (downtimePercentage / 100) * secondsPerYear;
      const monthlyDowntimeSeconds = (downtimePercentage / 100) * secondsPerMonth;
      const weeklyDowntimeSeconds = (downtimePercentage / 100) * secondsPerWeek;
      const dailyDowntimeSeconds = (downtimePercentage / 100) * secondsPerDay;

      // Format and display results
      this.displayResult('yearlyDowntime', this.formatTime(yearlyDowntimeSeconds));
      this.displayResult('monthlyDowntime', this.formatTime(monthlyDowntimeSeconds));
      this.displayResult('weeklyDowntime', this.formatTime(weeklyDowntimeSeconds));
      this.displayResult('dailyDowntime', this.formatTime(dailyDowntimeSeconds));

      this.showResults('uptime');
    }

    calculateFromDowntime() {
      const downtimeValue = parseFloat(this.downtimeInput.value || '0');

      if (!this.validateInput(downtimeValue, 'downtime')) {
        this.hideResults();
        return;
      }

      this.clearError('downtime');

      // Calculate uptime percentage
      const uptimePercentage = 100 - downtimeValue;

      // Time periods in seconds
      const secondsPerYear = 365.25 * 24 * 60 * 60; // Account for leap years
      const secondsPerMonth = 30.44 * 24 * 60 * 60; // Average month
      const secondsPerWeek = 7 * 24 * 60 * 60;
      const secondsPerDay = 24 * 60 * 60;

      // Calculate uptime in seconds for each period
      const yearlyUptimeSeconds = (uptimePercentage / 100) * secondsPerYear;
      const monthlyUptimeSeconds = (uptimePercentage / 100) * secondsPerMonth;
      const weeklyUptimeSeconds = (uptimePercentage / 100) * secondsPerWeek;
      const dailyUptimeSeconds = (uptimePercentage / 100) * secondsPerDay;

      // Format and display results
      this.displayResult('yearlyUptime', this.formatTime(yearlyUptimeSeconds));
      this.displayResult('monthlyUptime', this.formatTime(monthlyUptimeSeconds));
      this.displayResult('weeklyUptime', this.formatTime(weeklyUptimeSeconds));
      this.displayResult('dailyUptime', this.formatTime(dailyUptimeSeconds));
      this.displayResult('calculatedUptime', `${uptimePercentage.toFixed(3)}%`);

      this.showResults('downtime');
    }

    formatTime(totalSeconds) {
      if (totalSeconds < 1) {
        return '0 detik';
      }

      const secondsPerMinute = 60;
      const secondsPerHour = 60 * secondsPerMinute;
      const secondsPerDay = 24 * secondsPerHour;

      const days = Math.floor(totalSeconds / secondsPerDay);
      const remainingAfterDays = totalSeconds % secondsPerDay;

      const hours = Math.floor(remainingAfterDays / secondsPerHour);
      const remainingAfterHours = remainingAfterDays % secondsPerHour;

      const minutes = Math.floor(remainingAfterHours / secondsPerMinute);
      const remainingSeconds = Math.round(remainingAfterHours % secondsPerMinute);

      const parts = [];

      if (days > 0) {
        parts.push(days + ' hari');
      }
      if (hours > 0) {
        parts.push(hours + ' jam');
      }
      if (minutes > 0) {
        parts.push(minutes + ' menit');
      }
      if (remainingSeconds > 0 && days === 0) { // Only show seconds if no days
        parts.push(remainingSeconds + ' detik');
      }

      return parts.length > 0 ? parts.join(' ') : '0 detik';
    }

    displayResult(elementId, value) {
      const element = document.getElementById(elementId);
      if (element) {
        element.textContent = value;
      }
    }

    showResults(type) {
      if (this.resultsContainer && this.placeholderContent) {
        // Hide placeholder and show results with animation
        this.placeholderContent.classList.add('hidden');
        this.resultsContainer.classList.remove('hidden');
        this.resultsContainer.classList.add('animate-fade-in');

        // Show appropriate results section
        this.uptimeResults.classList.toggle('hidden', type !== 'uptime');
        this.downtimeResults.classList.toggle('hidden', type !== 'downtime');
      }
    }

    hideResults() {
      if (this.resultsContainer && this.placeholderContent) {
        // Hide results and show placeholder
        this.resultsContainer.classList.add('hidden');
        this.resultsContainer.classList.remove('animate-fade-in');
        this.placeholderContent.classList.remove('hidden');
      }
    }
  }

  // Initialize calculator when DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    console.log('Initializing Dual Calculator...');
    new DualCalculator();
  });
</script>